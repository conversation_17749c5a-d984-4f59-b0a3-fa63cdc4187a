# Docker Compose Environment Variables
# Copy this file to .env for local development
# Update values for production deployment

# Application Environment
NODE_ENV=development

# Database Configuration
MONGODB_ROOT_USERNAME=admin
MONGODB_ROOT_PASSWORD=secure_mongo_password_change_in_production
MONGODB_DATABASE=order_management_db

# Redis Configuration
REDIS_PASSWORD=secure_redis_password_change_in_production

# Security Keys (CHANGE ALL OF THESE IN PRODUCTION)
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars-required
JWT_EXPIRES_IN=7d
BCRYPT_SALT_ROUNDS=12
ENCRYPTION_KEY=your-32-char-encryption-key-change-in-production-required
API_KEY_SECRET=your-api-key-secret-change-in-production-required
SESSION_SECRET=your-session-secret-change-in-production-min-32-chars-required

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (Optional)
EMAIL_SERVICE_API_KEY=
EMAIL_FROM=<EMAIL>

# External Services (Optional)
PAYMENT_GATEWAY_SECRET=
WEBHOOK_SECRET=
